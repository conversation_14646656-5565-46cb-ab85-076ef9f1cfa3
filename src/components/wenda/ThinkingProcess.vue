<template>
  <div class="thinking-process">
    <div class="thinking-header">
      <div class="thinking-avatar">
        <img src="@/assets/assistant/董会答.png" alt="董会答头像" />
      </div>
      <div class="thinking-title">董会答正在思考中...</div>
    </div>
    
    <div class="thinking-content">
      <div class="thinking-steps">
        <div class="thinking-step" :class="{ active: currentStep >= 1 }">
          <div class="step-icon">🔍</div>
          <div class="step-text">分析问题</div>
        </div>
        
        <div class="thinking-step" :class="{ active: currentStep >= 2 }">
          <div class="step-icon">📚</div>
          <div class="step-text">搜索相关信息</div>
        </div>
        
        <div class="thinking-step" :class="{ active: currentStep >= 3 }">
          <div class="step-icon">💡</div>
          <div class="step-text">整理答案</div>
        </div>
      </div>
      
      <div class="thinking-dots">
        <span class="dot"></span>
        <span class="dot"></span>
        <span class="dot"></span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

// 当前思考步骤
const currentStep = ref(0);

// 定时器引用
let stepTimer: NodeJS.Timeout | null = null;

// 模拟思考步骤进度
const simulateThinking = () => {
  stepTimer = setInterval(() => {
    if (currentStep.value < 3) {
      currentStep.value++;
    }
  }, 800);
};

onMounted(() => {
  // 开始模拟思考过程
  setTimeout(() => {
    simulateThinking();
  }, 300);
});

onUnmounted(() => {
  if (stepTimer) {
    clearInterval(stepTimer);
    stepTimer = null;
  }
});
</script>

<style lang="scss" scoped>
.thinking-process {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin: 16px 0;
  border: 2px solid var(--border-accent, #e0e0e0);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.thinking-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;

  .thinking-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid var(--border-accent, #e0e0e0);
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .thinking-title {
    color: #333;
    font-size: 18px;
    font-weight: 600;
  }
}

.thinking-content {
  .thinking-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    gap: 16px;

    .thinking-step {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      flex: 1;
      opacity: 0.3;
      transition: all 0.5s ease;

      &.active {
        opacity: 1;
        transform: scale(1.05);
      }

      .step-icon {
        font-size: 24px;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 188, 212, 0.1);
        border-radius: 50%;
        border: 2px solid rgba(0, 188, 212, 0.3);
      }

      .step-text {
        font-size: 14px;
        color: #666;
        text-align: center;
        font-weight: 500;
      }

      &.active {
        .step-icon {
          background: rgba(0, 188, 212, 0.2);
          border-color: #00bcd4;
          animation: pulse 1.5s ease-in-out infinite;
        }

        .step-text {
          color: #333;
        }
      }
    }
  }

  .thinking-dots {
    display: flex;
    justify-content: center;
    gap: 6px;
    align-items: center;

    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #00bcd4;
      animation: thinkingPulse 1.5s ease-in-out infinite;

      &:nth-child(1) {
        animation-delay: 0s;
      }

      &:nth-child(2) {
        animation-delay: 0.3s;
      }

      &:nth-child(3) {
        animation-delay: 0.6s;
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes thinkingPulse {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}
</style>
